{"name": "flowdeck", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "list": "node scripts/dev.js list", "generate": "node scripts/dev.js generate", "parse-prd": "node scripts/dev.js parse-prd"}, "dependencies": {"@anthropic-ai/sdk": "^0.39.0", "@clerk/nextjs": "^6.19.4", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.11", "@types/uuid": "^10.0.0", "boxen": "^8.0.1", "chalk": "^4.1.2", "class-variance-authority": "^0.7.1", "cli-table3": "^0.6.5", "clsx": "^2.1.1", "commander": "^11.1.0", "convex": "^1.24.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.21.2", "fastmcp": "^1.20.5", "figlet": "^1.8.0", "fuse.js": "^7.0.0", "gradient-string": "^3.0.0", "helmet": "^8.1.0", "inquirer": "^12.5.0", "jsonwebtoken": "^9.0.2", "lru-cache": "^10.2.0", "lucide-react": "^0.511.0", "next": "15.3.2", "next-themes": "^0.4.6", "openai": "^4.89.0", "ora": "^8.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "react-markdown": "^10.1.0", "shadcn": "^2.5.0", "tailwind-merge": "^3.3.0", "uuid": "^11.1.0", "zod": "^3.25.20"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@tailwindcss/typography": "^0.5.16", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "tw-animate-css": "^1.3.0", "typescript": "^5"}, "type": "module"}