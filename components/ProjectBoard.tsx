"use client";

import { useState, useEffect, useMemo } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel";
import { Skeleton } from "@/components/ui/skeleton";
import { Input } from "@/components/ui/input";
import { Search, X } from "lucide-react";
import { Project, Milestone, Story } from "@/lib/types";
import ProjectHeader from "@/components/ProjectHeader";
import MilestonesRow from "@/components/MilestonesRow";
import StoriesGrid from "@/components/StoriesGrid";
import MilestoneCreateDialog from "@/components/MilestoneCreateDialog";
import StoryCreateDialog from "@/components/StoryCreateDialog";
import { ProjectStatus, ProjectPriority } from "@/types/project";

interface ProjectBoardProps {
  projectId: Id<"projects">;
}

export default function ProjectBoard({ projectId }: ProjectBoardProps) {
  // Hooks must be called at the top level and in the same order.
  const fetchedProjectData = useQuery(api.projects.getById, { projectId });
  
  // State for core project details (name, description, status, priority, id)
  const [projectDetails, setProjectDetails] = useState<{
    id: Id<"projects">;
    name: string;
    description: string;
    status: ProjectStatus;
    priority: ProjectPriority;
    team?: string;
    lead?: string;
    startDate?: number;
    endDate?: number;
  } | null>(null);
  
  // State for milestones - fetched from Convex
  const [milestones, setMilestones] = useState<Milestone[]>([]);
  const [isLoadingMilestones, setIsLoadingMilestones] = useState(true);
  
  // State for create dialogs
  const [isMilestoneCreateDialogOpen, setIsMilestoneCreateDialogOpen] = useState(false);
  const [isStoryCreateDialogOpen, setIsStoryCreateDialogOpen] = useState(false);
  const [selectedMilestoneIdForStory, setSelectedMilestoneIdForStory] = useState<string>("");

  // State for search functionality
  const [searchQuery, setSearchQuery] = useState("");

  // Mutations
  const performUpdateProjectDetails = useMutation(api.projects.updateProjectDetails);
  const createMilestoneMutation = useMutation(api.milestones.createMilestone);
  const updateMilestoneMutation = useMutation(api.milestones.updateMilestone);
  const deleteMilestoneMutation = useMutation(api.milestones.deleteMilestone);
  const createStoryMutation = useMutation(api.stories.createStory);
  const updateStoryMutation = useMutation(api.stories.updateStory);
  const deleteStoryMutation = useMutation(api.stories.deleteStory);
  
  const [isUpdatingProject, setIsUpdatingProject] = useState(false);
  
  // Fetch milestones from Convex
  const fetchedMilestones = useQuery(api.milestones.getByProject, 
    projectId ? { projectId } : "skip");
    
  // Fetch stories for each milestone
  const fetchedStoriesByProject = useQuery(api.stories.getByProject,
    projectId ? { projectId } : "skip");

  // Effect to synchronize fetched project data with local projectDetails state
  useEffect(() => {
    if (fetchedProjectData) {
      setProjectDetails({
        id: fetchedProjectData._id,
        name: fetchedProjectData.name,
        description: fetchedProjectData.description || "",
        status: (fetchedProjectData.status as ProjectStatus) || "Backlog", // Default to "Backlog" for existing projects
        priority: (fetchedProjectData.priority as ProjectPriority) || "No priority", // Default to "No priority"
        team: fetchedProjectData.team,
        lead: fetchedProjectData.lead,
        startDate: fetchedProjectData.startDate,
        endDate: fetchedProjectData.endDate
      });
    } else if (fetchedProjectData === null) {
      setProjectDetails(null); // Project not found
      setMilestones([]); // Clear milestones if project not found
    }
    // When fetchedProjectData is undefined (loading), projectDetails remains null, handled by loading UI.
  }, [fetchedProjectData]);
  
  // Effect to process fetched milestones and stories
  useEffect(() => {
    if (fetchedMilestones && fetchedStoriesByProject) {
      setIsLoadingMilestones(true);
      
      // Map the stories to their respective milestones
      const processedMilestones = fetchedMilestones.map(milestone => {
        // Find stories that belong to this milestone
        const milestoneStories = fetchedStoriesByProject
          .filter(story => story.milestoneId.toString() === milestone._id.toString())
          .map(story => ({
            id: story._id.toString(),
            name: story.name,
            background: story.background || "",
            acceptanceCriteria: story.acceptanceCriteria || "",
            milestoneId: story.milestoneId.toString(),
            tags: story.tags || [],
            storyPoints: story.storyPoints
          }));
          
        // Sort stories by order if available
        milestoneStories.sort((a, b) => {
          const storyA = fetchedStoriesByProject.find(s => s._id.toString() === a.id);
          const storyB = fetchedStoriesByProject.find(s => s._id.toString() === b.id);
          return (storyA?.order ?? 0) - (storyB?.order ?? 0);
        });
        
        return {
          id: milestone._id.toString(),
          name: milestone.name,
          description: milestone.description || "",
          color: milestone.color || "blue",
          stories: milestoneStories
        };
      });
      
      // Sort milestones by order if available
      processedMilestones.sort((a, b) => {
        const milestoneA = fetchedMilestones.find(m => m._id.toString() === a.id);
        const milestoneB = fetchedMilestones.find(m => m._id.toString() === b.id);
        return (milestoneA?.order ?? 0) - (milestoneB?.order ?? 0);
      });
      
      setMilestones(processedMilestones);
      setIsLoadingMilestones(false);
    }
  }, [fetchedMilestones, fetchedStoriesByProject]);

  // Filter milestones and stories based on search query
  const filteredMilestones = useMemo(() => {
    if (!searchQuery.trim()) {
      return milestones;
    }

    const query = searchQuery.toLowerCase().trim();

    return milestones.map(milestone => {
      // Filter stories within this milestone
      const filteredStories = milestone.stories.filter(story => {
        // Search in story name
        if (story.name.toLowerCase().includes(query)) return true;

        // Search in story background
        if (story.background && story.background.toLowerCase().includes(query)) return true;

        // Search in acceptance criteria
        if (story.acceptanceCriteria && story.acceptanceCriteria.toLowerCase().includes(query)) return true;

        // Search in tags
        if (story.tags && story.tags.some(tag => tag.toLowerCase().includes(query))) return true;

        return false;
      });

      // Return milestone with filtered stories
      return {
        ...milestone,
        stories: filteredStories
      };
    }).filter(milestone =>
      // Keep milestones that either have matching stories or match the milestone name
      milestone.stories.length > 0 || milestone.name.toLowerCase().includes(query)
    );
  }, [milestones, searchQuery]);

  // Define all handler functions unconditionally
  const updateProjectDetails = async (
    name: string,
    description: string,
    status: ProjectStatus,
    priority: ProjectPriority,
    team?: string,
    lead?: string,
    startDate?: number,
    endDate?: number
  ) => {
    if (!projectDetails) return; // Should not happen if UI allows editing

    setIsUpdatingProject(true);
    try {
      await performUpdateProjectDetails({
        projectId: projectDetails.id, // This is the Convex _id
        name: name,
        description: description,
        status: status,
        priority: priority,
        team: team,
        lead: lead,
        startDate: startDate,
        endDate: endDate,
      });
      // Optimistic update or rely on Convex query to refetch:
      // setProjectDetails(prev => prev ? { ...prev, name, description, status } : null);
      console.log("Project details updated successfully.");
    } catch (error) {
      console.error("Failed to update project details:", error);
      // TODO: Show error to user (e.g., using a toast notification)
    } finally {
      setIsUpdatingProject(false);
    }
  };

  // Function to open the milestone create dialog
  const openMilestoneCreateDialog = () => {
    if (!projectDetails) return;
    setIsMilestoneCreateDialogOpen(true);
  };

  // Function to create a new milestone
  const createMilestone = async (milestoneData: Omit<Milestone, "id" | "stories">) => {
    if (!projectDetails) return;
    
    try {
      // Optimistic UI update
      const tempId = `temp-${Date.now()}`;
      const newMilestone: Milestone = {
        id: tempId,
        name: milestoneData.name,
        description: milestoneData.description,
        color: milestoneData.color,
        stories: [],
      };
      
      setMilestones(prevMilestones => [...prevMilestones, newMilestone]);
      
      // Create milestone in Convex
      await createMilestoneMutation({
        projectId: projectDetails.id,
        name: milestoneData.name,
        description: milestoneData.description,
        color: milestoneData.color,
      });
      
      // Note: The useEffect will update the milestones when the query refreshes
    } catch (error) {
      console.error("Failed to create milestone:", error);
      // Revert optimistic update on error
      setMilestones(prevMilestones => 
        prevMilestones.filter(m => !m.id.startsWith('temp-'))
      );
    }
  };

  // Function to update a milestone
  const updateMilestone = async (milestoneId: string, updates: Partial<Milestone>) => {
    // Optimistic UI update
    setMilestones(prevMilestones =>
      prevMilestones.map(milestone =>
        milestone.id === milestoneId
          ? { ...milestone, ...updates }
          : milestone
      )
    );
    
    try {
      // Skip Convex update for temporary milestones
      if (milestoneId.startsWith('temp-')) return;
      
      // Update in Convex
      await updateMilestoneMutation({
        milestoneId: milestoneId as Id<"milestones">,
        name: updates.name,
        description: updates.description,
        color: updates.color,
      });
    } catch (error) {
      console.error("Failed to update milestone:", error);
      // Could revert optimistic update here if needed
    }
  };

  // Function to delete a milestone
  const deleteMilestone = async (milestoneId: string) => {
    // Store the milestone before removing it for potential rollback
    const milestoneToDelete = milestones.find(m => m.id === milestoneId);
    
    // Optimistic UI update
    setMilestones(prevMilestones =>
      prevMilestones.filter(milestone => milestone.id !== milestoneId)
    );
    
    try {
      // Skip Convex delete for temporary milestones
      if (milestoneId.startsWith('temp-')) return;
      
      // Delete in Convex
      await deleteMilestoneMutation({
        milestoneId: milestoneId as Id<"milestones">
      });
    } catch (error) {
      console.error("Failed to delete milestone:", error);
      
      // Revert optimistic update on error if we have the milestone data
      if (milestoneToDelete) {
        setMilestones(prevMilestones => [...prevMilestones, milestoneToDelete]);
      }
    }
  };

  // Function to open the story create dialog for a specific milestone
  const openStoryCreateDialog = (milestoneId: string) => {
    // Skip if the milestone doesn't exist
    const milestone = milestones.find(m => m.id === milestoneId);
    if (!milestone) return;
    
    setSelectedMilestoneIdForStory(milestoneId);
    setIsStoryCreateDialogOpen(true);
  };

  // Function to create a new story
  const createStory = async (milestoneId: string, storyData: Omit<Story, "id" | "milestoneId">) => {
    // Skip if the milestone doesn't exist
    const milestone = milestones.find(m => m.id === milestoneId);
    if (!milestone) return;
    
    // Create a temporary story for optimistic UI
    const tempId = `temp-${Date.now()}`;
    const newStory: Story = {
      id: tempId,
      name: storyData.name,
      milestoneId: milestoneId,
      tags: storyData.tags || [],
      background: storyData.background || "",
      acceptanceCriteria: storyData.acceptanceCriteria || "",
      storyPoints: storyData.storyPoints
    };
    
    // Optimistic UI update
    setMilestones(prevMilestones =>
      prevMilestones.map(milestone =>
        milestone.id === milestoneId
          ? { ...milestone, stories: [...milestone.stories, newStory] }
          : milestone
      )
    );
    
    try {
      // Skip Convex create for temporary milestones
      if (milestoneId.startsWith('temp-')) return;
      
      // Create in Convex
      await createStoryMutation({
        milestoneId: milestoneId as Id<"milestones">,
        name: storyData.name,
        background: storyData.background || "",
        acceptanceCriteria: storyData.acceptanceCriteria || "",
        tags: storyData.tags || [],
        storyPoints: storyData.storyPoints
      });
    } catch (error) {
      console.error("Failed to create story:", error);
      
      // Revert optimistic update on error
      setMilestones(prevMilestones =>
        prevMilestones.map(milestone =>
          milestone.id === milestoneId
            ? { 
                ...milestone, 
                stories: milestone.stories.filter(s => s.id !== tempId)
              }
            : milestone
        )
      );
    }
  };

  // Function to update a story
  const updateStory = async (
    originalMilestoneId: string,
    storyId: string,
    updates: Partial<Story>
  ) => {
    // Optimistic UI update
    setMilestones(prevMilestones => {
      const targetMilestoneId = updates.milestoneId || originalMilestoneId;

      // If milestone is not changing, just update the story in place
      if (targetMilestoneId === originalMilestoneId) {
        return prevMilestones.map(m =>
          m.id === originalMilestoneId
            ? {
                ...m,
                stories: m.stories.map(s =>
                  s.id === storyId ? { ...s, ...updates } : s
                ),
              }
            : m
        );
      }

      // If milestone is changing, find the story, remove from old, add to new
      let storyToMove: Story | undefined;
      const milestonesWithoutStory = prevMilestones.map(m => {
        if (m.id === originalMilestoneId) {
          storyToMove = m.stories.find(s => s.id === storyId);
          return {
            ...m,
            stories: m.stories.filter(s => s.id !== storyId),
          };
        }
        return m;
      });

      if (!storyToMove) return prevMilestones; // Story not found, should not happen

      const updatedStory = { ...storyToMove, ...updates, milestoneId: targetMilestoneId };

      return milestonesWithoutStory.map(m => {
        if (m.id === targetMilestoneId) {
          return {
            ...m,
            stories: [...m.stories, updatedStory],
          };
        }
        return m;
      });
    });
    
    try {
      // Skip Convex update for temporary stories or milestones
      if (storyId.startsWith('temp-') || originalMilestoneId.startsWith('temp-')) return;
      
      // Prepare update data for Convex
      const updateData: any = {
        storyId: storyId as Id<"stories">,
      };
      
      // Only include fields that were provided in the updates
      if (updates.name !== undefined) updateData.name = updates.name;
      if (updates.background !== undefined) updateData.background = updates.background;
      if (updates.acceptanceCriteria !== undefined) updateData.acceptanceCriteria = updates.acceptanceCriteria;
      if (updates.tags !== undefined) updateData.tags = updates.tags;
      if (updates.storyPoints !== undefined) updateData.storyPoints = updates.storyPoints;
      
      // If milestone is changing, include the new milestoneId
      if (updates.milestoneId && updates.milestoneId !== originalMilestoneId) {
        updateData.milestoneId = updates.milestoneId as Id<"milestones">;
      }
      
      // Update in Convex
      await updateStoryMutation(updateData);
    } catch (error) {
      console.error("Failed to update story:", error);
      // Could revert optimistic update here if needed
    }
  };

  // Function to delete a story
  const deleteStory = async (milestoneId: string, storyId: string) => {
    // Find the story before deleting for potential rollback
    let storyToDelete: Story | undefined;
    milestones.forEach(milestone => {
      if (milestone.id === milestoneId) {
        storyToDelete = milestone.stories.find(s => s.id === storyId);
      }
    });
    
    // Optimistic UI update
    setMilestones(prevMilestones =>
      prevMilestones.map(milestone =>
        milestone.id === milestoneId
          ? {
              ...milestone,
              stories: milestone.stories.filter(s => s.id !== storyId),
            }
          : milestone
      )
    );
    
    try {
      // Skip Convex delete for temporary stories or milestones
      if (storyId.startsWith('temp-') || milestoneId.startsWith('temp-')) return;
      
      // Delete in Convex
      await deleteStoryMutation({
        storyId: storyId as Id<"stories">
      });
    } catch (error) {
      console.error("Failed to delete story:", error);
      
      // Revert optimistic update on error if we have the story data
      if (storyToDelete) {
        setMilestones(prevMilestones =>
          prevMilestones.map(milestone =>
            milestone.id === milestoneId
              ? {
                  ...milestone,
                  stories: [...milestone.stories, storyToDelete!],
                }
              : milestone
          )
        );
      }
    }
  };

  // Helper function to get a random color for new milestones
  const getRandomColor = () => {
    const colors = [
      "blue", "green", "yellow", "purple", "pink", "orange", "red",
      "indigo", "teal", "cyan", "emerald", "lime", "amber", "rose", "fuchsia", "violet", "sky"
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  };

  // Conditional rendering after all hooks
  if (fetchedProjectData === undefined) {
    return (
      <div className="p-4 md:p-6">
        <div className="space-y-2 mb-6">
          <Skeleton className="h-8 w-3/4" />
          <Skeleton className="h-6 w-1/2" />
        </div>
        <Skeleton className="h-24 w-full mb-6" /> 
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }

  if (fetchedProjectData === null || projectDetails === null) {
    return (
      <div className="p-4 md:p-6">
        <h1 className="text-2xl font-bold text-destructive">Project Not Found</h1>
        <p className="text-muted-foreground">The requested project could not be found or you may not have permission to view it.</p>
      </div>
    );
  }

  // Main content render when data is available
  return (
    <div className="flex flex-col w-full h-full min-h-screen p-2 sm:p-4 md:p-6 space-y-4 sm:space-y-6">
      <ProjectHeader
        projectName={projectDetails.name} // Use name from projectDetails state
        projectDescription={projectDetails.description}
        projectStatus={projectDetails.status}
        projectPriority={projectDetails.priority}
        projectTeam={projectDetails.team}
        projectLead={projectDetails.lead}
        projectStartDate={projectDetails.startDate}
        projectEndDate={projectDetails.endDate}
        onUpdateProject={updateProjectDetails}
        isUpdating={isUpdatingProject}
      />

      {/* Search Field */}
      <div className="w-full max-w-md mx-auto">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            type="text"
            placeholder="Search stories by name, description, criteria, or tags..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 pr-10 rounded-2xl"
          />
          {searchQuery && (
            <button
              onClick={() => setSearchQuery("")}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
              aria-label="Clear search"
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>

        {/* Search Results Info */}
        {searchQuery && (
          <div className="text-center text-sm text-muted-foreground mt-2">
            {(() => {
              const totalStories = filteredMilestones.reduce((sum, milestone) => sum + milestone.stories.length, 0);
              const totalMilestones = filteredMilestones.length;
              return totalStories > 0
                ? `Found ${totalStories} ${totalStories === 1 ? 'story' : 'stories'} in ${totalMilestones} ${totalMilestones === 1 ? 'milestone' : 'milestones'}`
                : 'No stories found matching your search';
            })()}
          </div>
        )}
      </div>

      {/* Main scrollable container for both milestones and stories */}
      <div className="w-full overflow-x-auto flex-1 scrollbar-thin scrollbar-thumb-rounded scrollbar-thumb-primary/20 scrollbar-track-transparent">
        <div className="min-w-max px-2">
          {/* Sticky header for milestones row */}
          <div className="sticky top-0 z-10 pt-2 pb-4 bg-background">
            <MilestonesRow
              milestones={searchQuery ? filteredMilestones : milestones} // Use filtered milestones when searching
              onAddMilestone={openMilestoneCreateDialog}
              onUpdateMilestone={updateMilestone}
              onDeleteMilestone={deleteMilestone}
            />
          </div>

          {/* Stories grid */}
          <div className="flex-1">
            <StoriesGrid
              milestones={searchQuery ? filteredMilestones : milestones} // Use filtered milestones when searching
              onAddStory={openStoryCreateDialog}
              onUpdateStory={updateStory}
              onDeleteStory={deleteStory}
            />
          </div>
        </div>
      </div>
      {/* Modal Dialogs */}
      <MilestoneCreateDialog
        open={isMilestoneCreateDialogOpen}
        onOpenChange={setIsMilestoneCreateDialogOpen}
        onCreate={createMilestone}
      />
      
      <StoryCreateDialog
        milestoneId={selectedMilestoneIdForStory}
        milestones={milestones}
        open={isStoryCreateDialogOpen}
        onOpenChange={setIsStoryCreateDialogOpen}
        onCreate={createStory}
      />
    </div>
  );
}
