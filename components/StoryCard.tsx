"use client";

import { useState } from "react";
import { Story, Milestone } from "@/lib/types";
import { Card, CardContent } from "@/components/ui/card";
import { Trash2 } from "lucide-react";
import StoryEditDialog from "./StoryEditDialog";
import ReactMarkdown from "react-markdown";

interface StoryCardProps {
  story: Story;
  milestones: Milestone[];
  milestoneColor?: string;
  onUpdate: (updates: Partial<Story>) => void;
  onDelete: () => void;
}

export default function StoryCard({
  story,
  milestones,
  milestoneColor = "gray",
  onUpdate,
  onDelete,
}: StoryCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);

  const handleCardClick = () => {
    setDialogOpen(true);
  };

  // Get subtle border color based on milestone color
  const getBorderColor = () => {
    const colorMap: Record<string, string> = {
      red: "border-red-200 dark:border-red-800",
      pink: "border-pink-200 dark:border-pink-800",
      rose: "border-rose-200 dark:border-rose-800",
      orange: "border-orange-200 dark:border-orange-800",
      amber: "border-amber-200 dark:border-amber-800",
      yellow: "border-yellow-200 dark:border-yellow-800",
      lime: "border-lime-200 dark:border-lime-800",
      green: "border-green-200 dark:border-green-800",
      emerald: "border-emerald-200 dark:border-emerald-800",
      teal: "border-teal-200 dark:border-teal-800",
      cyan: "border-cyan-200 dark:border-cyan-800",
      sky: "border-sky-200 dark:border-sky-800",
      blue: "border-blue-200 dark:border-blue-800",
      indigo: "border-indigo-200 dark:border-indigo-800",
      violet: "border-violet-200 dark:border-violet-800",
      purple: "border-purple-200 dark:border-purple-800",
      fuchsia: "border-fuchsia-200 dark:border-fuchsia-800",
      gray: "border-gray-200 dark:border-gray-700",
    };
    
    return colorMap[milestoneColor] || "border-gray-200 dark:border-gray-700";
  };

  // Get subtle background color based on milestone color
  const getBackgroundColor = () => {
    const colorMap: Record<string, string> = {
      red: "bg-red-50/70 dark:bg-red-950/20 hover:bg-red-400/10 dark:hover:bg-red-700/10",
      pink: "bg-pink-50/70 dark:bg-pink-950/20 hover:bg-pink-400/10 dark:hover:bg-pink-700/10",
      rose: "bg-rose-50/70 dark:bg-rose-950/20 hover:bg-rose-400/10 dark:hover:bg-rose-700/10",
      orange: "bg-orange-50/70 dark:bg-orange-950/20 hover:bg-orange-400/10 dark:hover:bg-orange-700/10",
      amber: "bg-amber-50/70 dark:bg-amber-950/20 hover:bg-amber-400/10 dark:hover:bg-amber-700/10",
      yellow: "bg-yellow-50/70 dark:bg-yellow-950/20 hover:bg-yellow-400/10 dark:hover:bg-yellow-700/10",
      lime: "bg-lime-50/70 dark:bg-lime-950/20 hover:bg-lime-400/10 dark:hover:bg-lime-700/10",
      green: "bg-green-50/70 dark:bg-green-950/20 hover:bg-green-400/10 dark:hover:bg-green-700/10",
      emerald: "bg-emerald-50/70 dark:bg-emerald-950/20 hover:bg-emerald-400/10 dark:hover:bg-emerald-700/10",
      teal: "bg-teal-50/70 dark:bg-teal-950/20 hover:bg-teal-400/10 dark:hover:bg-teal-700/10",
      cyan: "bg-cyan-50/70 dark:bg-cyan-950/20 hover:bg-cyan-400/10 dark:hover:bg-cyan-700/10",
      sky: "bg-sky-50/70 dark:bg-sky-950/20 hover:bg-sky-400/10 dark:hover:bg-sky-700/10",
      blue: "bg-blue-50/70 dark:bg-blue-950/20 hover:bg-blue-400/10 dark:hover:bg-blue-700/10",
      indigo: "bg-indigo-50/70 dark:bg-indigo-950/20 hover:bg-indigo-400/10 dark:hover:bg-indigo-700/10",
      violet: "bg-violet-50/70 dark:bg-violet-950/20 hover:bg-violet-400/10 dark:hover:bg-violet-700/10",
      purple: "bg-purple-50/70 dark:bg-purple-950/20 hover:bg-purple-400/10 dark:hover:bg-purple-700/10",
      fuchsia: "bg-fuchsia-50/70 dark:bg-fuchsia-950/20 hover:bg-fuchsia-400/10 dark:hover:bg-fuchsia-700/10",
      gray: "bg-gray-50/70 dark:bg-gray-800/10 hover:bg-gray-50 dark:hover:bg-gray-800/50",
    };
    
    return colorMap[milestoneColor] || "bg-white dark:bg-[oklch(0.2466_0.0316_260.4/0.3)] hover:bg-gray-50 dark:hover:bg-gray-800/50";
  };

  // Get tag colors based on milestone color
  const getTagColors = () => {
    const colorMap: Record<string, string> = {
      red: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300",
      pink: "bg-pink-100 text-pink-800 dark:bg-pink-900/30 dark:text-pink-300",
      rose: "bg-rose-100 text-rose-800 dark:bg-rose-900/30 dark:text-rose-300",
      orange: "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300",
      amber: "bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300",
      yellow: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300",
      lime: "bg-lime-100 text-lime-800 dark:bg-lime-900/30 dark:text-lime-300",
      green: "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
      emerald: "bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-300",
      teal: "bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-300",
      cyan: "bg-cyan-100 text-cyan-800 dark:bg-cyan-900/30 dark:text-cyan-300",
      sky: "bg-sky-100 text-sky-800 dark:bg-sky-900/30 dark:text-sky-300",
      blue: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
      indigo: "bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300",
      violet: "bg-violet-100 text-violet-800 dark:bg-violet-900/30 dark:text-violet-300",
      purple: "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300",
      fuchsia: "bg-fuchsia-100 text-fuchsia-800 dark:bg-fuchsia-900/30 dark:text-fuchsia-300",
      gray: "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300",
    };
    
    return colorMap[milestoneColor] || "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300";
  };

  return (
    <>
      <Card 
        className={`rounded-2xl shadow-md border ${getBorderColor()} ${getBackgroundColor()} transition-colors`}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onTouchStart={() => setIsHovered(true)}
        onTouchEnd={() => setTimeout(() => setIsHovered(false), 1000)}
        onClick={handleCardClick}
      >
        <CardContent className="px-2 pt-0.5 pb-1 sm:px-3 sm:pt-1 sm:pb-2 relative">
          {/* Story Title */}
          <div className="text-xs sm:text-lg font-medium cursor-pointer hover:text-primary transition-colors mb-3 text-center">
            {story.name}
          </div>
          
          {/* Background (truncated) */}
          {story.background && (
            <div className="mt-0.5">
              <div className="text-xs font-medium mb-1.5">Background:</div>
              <div className="text-xs text-muted-foreground">
                {(() => {
                  const firstLine = story.background.split('\n')[0];
                  const hasMoreContent = story.background.includes('\n') || firstLine.length > 60;
                  const displayText = firstLine.length > 60 ? firstLine.substring(0, 60) : firstLine;
                  
                  return (
                    <span>
                      {displayText}
                      {hasMoreContent && '...'}
                    </span>
                  );
                })()}
              </div>
            </div>
          )}
          
          {/* Acceptance Criteria (truncated) */}
          {story.acceptanceCriteria && (
            <div className="mt-3">
              <div className="text-xs font-medium mb-1.5">Acceptance:</div>
              <div className="text-xs text-muted-foreground">
                {(() => {
                  // Find the first bullet point
                  const lines = story.acceptanceCriteria.split('\n');
                  let firstBulletLine = '';
                  let bulletContent = '';
                  
                  // Look for the first bullet point
                  for (let i = 0; i < lines.length; i++) {
                    if (lines[i].trim().startsWith('- ')) {
                      firstBulletLine = lines[i].trim();
                      bulletContent = firstBulletLine.substring(2);
                      break;
                    }
                  }
                  
                  // If we found a bullet point
                  if (bulletContent) {
                    // Truncate if needed
                    const hasMoreContent = story.acceptanceCriteria.split('\n').length > 1 || bulletContent.length > 60;
                    const displayText = bulletContent.length > 60 ? bulletContent.substring(0, 60) : bulletContent;
                    
                    return (
                      <div className="flex items-start">
                        <span className="mr-1.5 inline-block">•</span>
                        <span>
                          {displayText.split('**').map((part: string, partIndex: number) => {
                            // Every odd index is inside ** and should be bold
                            return partIndex % 2 === 1 ? 
                              <strong key={partIndex} className="font-semibold">{part}</strong> : 
                              <span key={partIndex}>{part}</span>;
                          })}
                          {hasMoreContent && '...'}
                        </span>
                      </div>
                    );
                  }
                  
                  // If no bullet point was found, just show the first line
                  if (lines.length > 0) {
                    const firstLine = lines[0];
                    const hasMoreContent = lines.length > 1 || firstLine.length > 60;
                    const displayText = firstLine.length > 60 ? firstLine.substring(0, 60) : firstLine;
                    
                    return (
                      <span>
                        {displayText.split('**').map((part: string, partIndex: number) => {
                          // Every odd index is inside ** and should be bold
                          return partIndex % 2 === 1 ? 
                            <strong key={partIndex} className="font-semibold">{part}</strong> : 
                            <span key={partIndex}>{part}</span>;
                        })}
                        {hasMoreContent && '...'}
                      </span>
                    );
                  }
                  
                  return null;
                })()}
              </div>
            </div>
          )}
          
          {/* Footer with Tags and Story Points */}
          <div className="flex justify-between items-center mt-2 pt-1 border-t border-border/30">
            {/* Tags */}
            <div className="flex flex-wrap gap-1 max-w-[80%]">
              {story.tags && story.tags.length > 0 && story.tags.map((tag, index) => (
                <span 
                  key={index} 
                  className={`${getTagColors()} text-xs px-1.5 py-0.5 rounded-full`}
                >
                  {tag}
                </span>
              ))}
            </div>
            
            {/* Story Points */}
            {story.storyPoints !== undefined && (
              <div className="text-xs font-medium bg-primary/10 text-primary rounded-full h-5 w-5 flex items-center justify-center ml-auto">
                {story.storyPoints}
              </div>
            )}
          </div>
          
          {/* Delete Button */}
          {isHovered && (
            <button
              onClick={(e) => {
                e.stopPropagation(); // Prevent card click
                onDelete();
              }}
              className="absolute top-1 sm:top-2 right-1 sm:right-2 text-gray-500 hover:text-red-500 transition-colors"
              aria-label="Delete story"
            >
              <Trash2 className="h-3 w-3 sm:h-4 sm:w-4" />
            </button>
          )}
        </CardContent>
      </Card>

      <StoryEditDialog
        story={story}
        milestones={milestones}
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        onUpdate={onUpdate}
      />
    </>
  );
}
