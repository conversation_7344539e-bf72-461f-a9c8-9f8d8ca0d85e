"use client";

import { useState, useEffect } from "react";
import { Story, Milestone } from "@/lib/types";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { 
  Ta<PERSON>, 
  TabsContent, 
  TabsList, 
  TabsTrigger 
} from "./ui/tabs";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select";
import { 
  FileText, 
  CheckSquare,
  Tag as TagIcon,
  X 
} from "lucide-react";

interface StoryEditDialogProps {
  story: Story;
  milestones: Milestone[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onUpdate: (updates: Partial<Story>) => void;
}

export default function StoryEditDialog({
  story,
  milestones,
  open,
  onOpenChange,
  onUpdate,
}: StoryEditDialogProps) {
  const [editedStory, setEditedStory] = useState<Partial<Story>>({
    name: story.name,
    background: story.background || "",
    acceptanceCriteria: story.acceptanceCriteria || "",
    milestoneId: story.milestoneId,
    tags: story.tags || [],
    storyPoints: story.storyPoints,
  });
  
  const [newTag, setNewTag] = useState<string>("");
  const [activeTab, setActiveTab] = useState<string>("background");

  // Reset form when dialog opens
  useEffect(() => {
    if (open) {
      setEditedStory({
        name: story.name,
        background: story.background || "",
        acceptanceCriteria: story.acceptanceCriteria || "",
        milestoneId: story.milestoneId,
        tags: story.tags || [],
        storyPoints: story.storyPoints,
      });
      setActiveTab("background");
    }
  }, [open, story]);

  const handleSave = () => {
    onUpdate(editedStory);
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  const handleAddTag = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && newTag.trim() !== "") {
      e.preventDefault();
      if (!editedStory.tags?.includes(newTag.trim())) {
        setEditedStory({
          ...editedStory,
          tags: [...(editedStory.tags || []), newTag.trim()],
        });
      }
      setNewTag("");
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setEditedStory({
      ...editedStory,
      tags: editedStory.tags?.filter(tag => tag !== tagToRemove) || [],
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Story</DialogTitle>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          {/* Title */}
          <div className="grid gap-2">
            <Label htmlFor="story-title">Title</Label>
            <Input
              id="story-title"
              value={editedStory.name}
              onChange={(e) => setEditedStory({ ...editedStory, name: e.target.value })}
            />
          </div>

          {/* Tabs for Background and Acceptance Criteria */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid grid-cols-2">
              <TabsTrigger value="background" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Background
              </TabsTrigger>
              <TabsTrigger value="acceptance-criteria" className="flex items-center gap-2">
                <CheckSquare className="h-4 w-4" />
                Acceptance Criteria
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="background" className="mt-4">
              <div className="grid gap-2">
                <Label htmlFor="story-background">Background (Markdown supported)</Label>
                <Textarea
                  id="story-background"
                  value={editedStory.background}
                  onChange={(e) => setEditedStory({ ...editedStory, background: e.target.value })}
                  className="min-h-[150px]"
                />
              </div>
            </TabsContent>
            
            <TabsContent value="acceptance-criteria" className="mt-4">
              <div className="grid gap-2">
                <Label htmlFor="story-acceptance-criteria">Acceptance Criteria (Markdown supported)</Label>
                <Textarea
                  id="story-acceptance-criteria"
                  value={editedStory.acceptanceCriteria}
                  onChange={(e) => setEditedStory({ ...editedStory, acceptanceCriteria: e.target.value })}
                  className="min-h-[150px]"
                />
              </div>
            </TabsContent>
          </Tabs>

          {/* Milestone Dropdown */}
          <div className="grid gap-2">
            <Label htmlFor="story-milestone">Milestone</Label>
            <Select 
              value={editedStory.milestoneId} 
              onValueChange={(value: string) => setEditedStory({ ...editedStory, milestoneId: value })}
            >
              <SelectTrigger id="story-milestone">
                <SelectValue placeholder="Select a milestone" />
              </SelectTrigger>
              <SelectContent>
                {milestones.map((milestone) => (
                  <SelectItem key={milestone.id} value={milestone.id}>
                    {milestone.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Story Points */}
          <div className="grid gap-2">
            <Label htmlFor="story-points">Story Points</Label>
            <Select 
              value={editedStory.storyPoints?.toString() || "none"} 
              onValueChange={(value: string) => setEditedStory({ 
                ...editedStory, 
                storyPoints: value === "none" ? undefined : parseInt(value, 10) 
              })}
            >
              <SelectTrigger id="story-points">
                <SelectValue placeholder="Assign story points" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">Not set</SelectItem>
                <SelectItem value="1">1</SelectItem>
                <SelectItem value="2">2</SelectItem>
                <SelectItem value="3">3</SelectItem>
                <SelectItem value="5">5</SelectItem>
                <SelectItem value="8">8</SelectItem>
                <SelectItem value="13">13</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Tags */}
          <div className="grid gap-2">
            <Label className="flex items-center gap-2" htmlFor="story-tags">
              <TagIcon className="h-4 w-4" />
              Tags
            </Label>
            <Input
              id="story-tags"
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              onKeyDown={handleAddTag}
              placeholder="Add tag and press Enter..."
            />
            
            {/* Display tags */}
            <div className="flex flex-wrap gap-2 mt-2">
              {editedStory.tags?.map((tag) => (
                <div 
                  key={tag} 
                  className="flex items-center gap-1 bg-secondary text-secondary-foreground px-2 py-1 rounded-md text-sm"
                >
                  {tag}
                  <button 
                    onClick={() => handleRemoveTag(tag)}
                    className="text-secondary-foreground/70 hover:text-secondary-foreground"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
