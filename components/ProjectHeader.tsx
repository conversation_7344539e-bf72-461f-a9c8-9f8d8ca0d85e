"use client";

import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Pencil, Users, User, Calendar } from "lucide-react";
import ProjectEditDialog from "./ProjectEditDialog";
import { ProjectStatus, getStatusColor, ProjectPriority, getPriorityColor } from "@/types/project";

interface ProjectHeaderProps {
  projectName: string;
  projectDescription: string;
  projectStatus?: ProjectStatus;
  projectPriority?: ProjectPriority;
  projectTeam?: string;
  projectLead?: string;
  projectStartDate?: number;
  projectEndDate?: number;
  onUpdateProject: (name: string, description: string, status: ProjectStatus, priority: ProjectPriority, team?: string, lead?: string, startDate?: number, endDate?: number) => Promise<void>;
  isUpdating?: boolean;
}

export default function ProjectHeader({
  projectName,
  projectDescription,
  projectStatus = "Backlog",
  projectPriority = "No priority",
  projectTeam,
  projectLead,
  projectStartDate,
  projectEndDate,
  onUpdateProject,
  isUpdating,
}: ProjectHeaderProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleOpenDialog = () => {
    setIsDialogOpen(true);
  };

  return (
    <div className="flex flex-col items-center w-full py-2 sm:py-2 space-y-2">
      <Card 
        className="w-full max-w-3xl cursor-pointer hover:shadow-md transition-shadow duration-200 group"
        onClick={handleOpenDialog}
      >
        <CardContent className="p-3 relative">
          {/* Header section with title and status/priority labels */}
          <div className="flex items-start justify-between mb-3">
            <h1 className="text-xl sm:text-xl md:text-2xl font-bold text-left flex-1 pr-4">
              {projectName}
            </h1>
            <div className="flex items-center gap-2 flex-shrink-0">
              <span className={`px-2 py-1 rounded-md text-xs font-medium ${getStatusColor(projectStatus)}`}>
                {projectStatus}
              </span>
              <span className={`px-2 py-1 rounded-md text-xs font-medium ${getPriorityColor(projectPriority)}`}>
                {projectPriority}
              </span>
            </div>
          </div>
          
          {/* Description */}
          <p className="text-sm text-muted-foreground mb-4 text-left">
            {projectDescription}
          </p>
          
          {/* Team information */}
          {(projectTeam || projectLead || projectStartDate) && (
            <div className="flex items-center gap-6 text-sm text-muted-foreground">
              {projectTeam && (
                <div className="flex items-center gap-1">
                  <Users className="h-4 w-4" />
                  <span>Team: {projectTeam}</span>
                </div>
              )}
              {projectLead && (
                <div className="flex items-center gap-1">
                  <User className="h-4 w-4" />
                  <span>Lead: {projectLead}</span>
                </div>
              )}
              {projectStartDate && (
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  <span>Start: {new Date(projectStartDate).toLocaleDateString()}</span>
                </div>
              )}
            </div>
          )}
          

        </CardContent>
      </Card>
      
      <ProjectEditDialog
        projectName={projectName}
        projectDescription={projectDescription}
        projectStatus={projectStatus}
        projectPriority={projectPriority}
        projectTeam={projectTeam}
        projectLead={projectLead}
        projectStartDate={projectStartDate}
        projectEndDate={projectEndDate}
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        onUpdate={onUpdateProject}
        isUpdating={isUpdating}
      />
    </div>
  );
}
