import { Project, Miles<PERSON>, Story } from "@/lib/types";

// Placeholder values for export as specified in requirements
const PLACEHOLDER_VALUES = {
  teamId: "50e71128-51a1-45f8-963e-9d35b458a205",
  stateId: "1310f4c8-55b4-42da-9a3b-45a71f8155e5", 
  projectId: "cd02dae1-b8a0-4f01-8026-4d55b9000c94",
  milestoneId: "0bd6b306-a04c-448a-bf09-48f297a6c84e"
};

/**
 * Converts a story to YAML format following the issues.yaml structure
 */
function storyToYaml(story: Story, indent: string = "  "): string {
  const lines: string[] = [];
  
  lines.push(`${indent}- title: "${story.name}"`);
  
  // Build description from background and acceptance criteria
  let description = "";
  if (story.background) {
    description += `# Background\n${story.background}\n\n`;
  }
  if (story.acceptanceCriteria) {
    description += `# Acceptance Criteria\n${story.acceptanceCriteria}`;
  }
  
  if (description) {
    lines.push(`${indent}  description: |`);
    // Split description into lines and add proper indentation
    const descriptionLines = description.split('\n');
    descriptionLines.forEach(line => {
      lines.push(`${indent}    ${line}`);
    });
    lines.push(''); // Empty line after description
  }
  
  // Add placeholder IDs
  lines.push(`${indent}  teamId: "${PLACEHOLDER_VALUES.teamId}"`);
  lines.push(`${indent}  stateId: "${PLACEHOLDER_VALUES.stateId}"`);
  lines.push(`${indent}  projectId: "${PLACEHOLDER_VALUES.projectId}"`);
  lines.push(`${indent}  milestoneId: "${PLACEHOLDER_VALUES.milestoneId}"`);
  
  return lines.join('\n');
}

/**
 * Exports project data to YAML format
 */
export function exportProjectToYaml(project: Project): string {
  const lines: string[] = [];
  
  lines.push('issues:');
  
  // Iterate through all milestones and their stories
  project.milestones.forEach(milestone => {
    milestone.stories.forEach(story => {
      lines.push(storyToYaml(story));
      lines.push(''); // Empty line between stories
    });
  });
  
  // Remove the last empty line if it exists
  if (lines[lines.length - 1] === '') {
    lines.pop();
  }
  
  return lines.join('\n');
}

/**
 * Triggers a download of the YAML content in the browser
 */
export function downloadYamlFile(yamlContent: string, filename: string = 'project-export.yaml'): void {
  // Create a blob with the YAML content
  const blob = new Blob([yamlContent], { type: 'text/yaml;charset=utf-8' });
  
  // Create a temporary URL for the blob
  const url = URL.createObjectURL(blob);
  
  // Create a temporary anchor element and trigger download
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  link.style.display = 'none';
  
  // Add to DOM, click, and remove
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  // Clean up the URL
  URL.revokeObjectURL(url);
}

/**
 * Main export function that combines YAML generation and download
 */
export function exportAndDownloadProject(project: Project, filename?: string): void {
  const yamlContent = exportProjectToYaml(project);
  const exportFilename = filename || `${project.name.toLowerCase().replace(/\s+/g, '-')}-export.yaml`;
  downloadYamlFile(yamlContent, exportFilename);
}
