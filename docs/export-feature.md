# Project Export Feature

## Overview

The FlowDeck application now includes a project export feature that allows users to export their project data (milestones and stories) in YAML format. This feature is useful for:

- Backing up project data
- Sharing project structure with team members
- Importing data into other systems
- Creating documentation from project data

## How to Use

1. **Navigate to a Project**: Open any project in FlowDeck by clicking on it from the dashboard.

2. **Find the Export Button**: In the project header (top of the page), you'll see an "Export" button with a download icon next to the project status and priority labels.

3. **Click Export**: Click the "Export" button to automatically generate and download a YAML file containing all project data.

4. **File Download**: The browser will automatically download a file named `{project-name}-export.yaml` to your default downloads folder.

## YAML Format

The exported YAML follows this structure:

```yaml
issues:
  - title: "[TASK] Story Title"
    description: |
      # Background
      Story background information here
      
      # Acceptance Criteria
      - Criterion 1
      - Criterion 2
      - Criterion 3
    
    teamId: "50e71128-51a1-45f8-963e-9d35b458a205"
    stateId: "1310f4c8-55b4-42da-9a3b-45a71f8155e5"
    projectId: "cd02dae1-b8a0-4f01-8026-4d55b9000c94"
    milestoneId: "0bd6b306-a04c-448a-bf09-48f297a6c84e"
```

### Field Descriptions

- **title**: The story name/title
- **description**: Combined background and acceptance criteria with proper formatting
- **teamId, stateId, projectId, milestoneId**: Placeholder UUID values as specified in requirements

## Technical Implementation

### Components Modified

1. **ProjectHeader.tsx**: Added export button with download icon
2. **ProjectBoard.tsx**: Added export functionality and data preparation
3. **lib/export-utils.ts**: New utility functions for YAML generation and file download

### Key Functions

- `exportProjectToYaml(project)`: Converts project data to YAML format
- `downloadYamlFile(content, filename)`: Triggers browser download
- `exportAndDownloadProject(project, filename?)`: Main export function

### Data Flow

1. User clicks Export button in ProjectHeader
2. ProjectBoard calls `handleExportProject()`
3. Project data (milestones and stories) is formatted into Project type
4. `exportAndDownloadProject()` generates YAML and triggers download
5. Browser downloads the file automatically

## Future Enhancements

Potential future improvements could include:

- Additional export formats (JSON, CSV, PDF)
- Custom filename selection
- Selective export (specific milestones or stories)
- Export scheduling/automation
- Integration with external project management tools

## Troubleshooting

**Export button not visible**: Ensure you're viewing a project page (not the dashboard) and that the project has been loaded successfully.

**Download not starting**: Check your browser's download settings and popup blockers. The feature uses the browser's built-in download functionality.

**Empty or malformed YAML**: Ensure your project has milestones and stories with proper data. Empty projects will generate minimal YAML output.
